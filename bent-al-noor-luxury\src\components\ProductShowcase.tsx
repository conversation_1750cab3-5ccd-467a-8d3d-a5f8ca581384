import { motion, useInView } from 'framer-motion'
import { useRef, useState } from 'react'
import { Heart, ShoppingBag, Eye } from 'lucide-react'

const ProductShowcase = () => {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null)

  const products = [
    {
      id: 1,
      name: "Celestial Silk",
      arabicName: "حرير سماوي",
      price: "$299",
      image: "https://images.unsplash.com/photo-1631737859822-d954fbb08f5f?fm=jpg&q=80&w=500&h=600&fit=crop&crop=center",
      description: "Luxurious silk scarf with celestial patterns",
      colors: ["#e8bc44", "#22c55e", "#1a1a1a"]
    },
    {
      id: 2,
      name: "Golden Threads",
      arabicName: "خيوط ذهبية",
      price: "$399",
      image: "https://images.unsplash.com/photo-1617055407123-3d7130c1f940?fm=jpg&q=80&w=500&h=600&fit=crop&crop=center",
      description: "Hand-embroidered with golden threads",
      colors: ["#f6e397", "#e8bc44", "#d4a332"]
    },
    {
      id: 3,
      name: "Emerald Grace",
      arabicName: "نعمة زمردية",
      price: "$349",
      image: "https://images.unsplash.com/photo-1683140426885-6c0ce899409c?fm=jpg&q=80&w=500&h=600&fit=crop&crop=center",
      description: "Elegant emerald-toned luxury scarf",
      colors: ["#22c55e", "#16a34a", "#15803d"]
    },
    {
      id: 4,
      name: "Royal Heritage",
      arabicName: "تراث ملكي",
      price: "$449",
      image: "https://images.unsplash.com/photo-1618434958571-459c9c972ae8?fm=jpg&q=80&w=500&h=600&fit=crop&crop=center",
      description: "Premium collection with royal patterns",
      colors: ["#1a1a1a", "#e8bc44", "#22c55e"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="collection" ref={ref} className="section-padding bg-gradient-to-b from-cream-50 to-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-5xl md:text-6xl font-bold text-luxury-gradient mb-6">
            Featured Collection
          </h2>
          <motion.div
            initial={{ width: 0 }}
            animate={isInView ? { width: "200px" } : {}}
            transition={{ duration: 1, delay: 0.3 }}
            className="h-1 bg-gradient-to-r from-transparent via-gold-500 to-transparent mx-auto mb-6"
          />
          <p className="text-xl text-charcoal-600 max-w-3xl mx-auto leading-relaxed">
            Each piece in our collection tells a story of craftsmanship, tradition, and modern elegance. 
            Discover scarves that embody the perfect harmony of modesty and luxury.
          </p>
        </motion.div>

        {/* Product Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              variants={itemVariants}
              onHoverStart={() => setHoveredProduct(product.id)}
              onHoverEnd={() => setHoveredProduct(null)}
              className="group relative luxury-card p-6 cursor-pointer"
            >
              {/* Product Image */}
              <div className="relative overflow-hidden rounded-xl mb-6 aspect-[3/4]">
                <motion.img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.6 }}
                />
                
                {/* Overlay Actions */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: hoveredProduct === product.id ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="absolute inset-0 bg-charcoal-900/40 flex items-center justify-center space-x-4"
                >
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-3 bg-white/90 rounded-full text-charcoal-700 hover:bg-white transition-colors"
                  >
                    <Eye size={20} />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-3 bg-white/90 rounded-full text-charcoal-700 hover:bg-white transition-colors"
                  >
                    <Heart size={20} />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-3 bg-gold-500 rounded-full text-white hover:bg-gold-600 transition-colors"
                  >
                    <ShoppingBag size={20} />
                  </motion.button>
                </motion.div>

                {/* New Badge */}
                <div className="absolute top-4 left-4 bg-gold-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  New
                </div>
              </div>

              {/* Product Info */}
              <div className="text-center">
                <h3 className="font-serif text-xl font-semibold text-charcoal-800 mb-1">
                  {product.name}
                </h3>
                <p className="font-script text-gold-600 mb-2 text-lg">
                  {product.arabicName}
                </p>
                <p className="text-charcoal-600 text-sm mb-4 leading-relaxed">
                  {product.description}
                </p>
                
                {/* Color Options */}
                <div className="flex justify-center space-x-2 mb-4">
                  {product.colors.map((color, colorIndex) => (
                    <motion.div
                      key={colorIndex}
                      whileHover={{ scale: 1.2 }}
                      className="w-4 h-4 rounded-full border-2 border-white shadow-md cursor-pointer"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>

                {/* Price */}
                <div className="text-2xl font-bold text-luxury-gradient mb-4">
                  {product.price}
                </div>

                {/* Add to Cart Button */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full btn-luxury text-sm py-3"
                >
                  Add to Collection
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="px-12 py-4 text-lg font-medium text-charcoal-700 border-2 border-gold-400 rounded-full hover:bg-gold-50 transition-all duration-300 magnetic"
          >
            View Complete Collection
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default ProductShowcase
