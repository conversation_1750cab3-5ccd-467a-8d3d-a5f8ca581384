{"version": 3, "sources": ["../../lenis/package.json", "../../lenis/packages/core/src/maths.ts", "../../lenis/packages/core/src/animate.ts", "../../lenis/packages/core/src/debounce.ts", "../../lenis/packages/core/src/dimensions.ts", "../../lenis/packages/core/src/emitter.ts", "../../lenis/packages/core/src/virtual-scroll.ts", "../../lenis/packages/core/src/lenis.ts"], "sourcesContent": ["{\n  \"name\": \"lenis\",\n  \"version\": \"1.3.4\",\n  \"description\": \"How smooth scroll should be\",\n  \"type\": \"module\",\n  \"sideEffects\": false,\n  \"author\": \"darkroom.engineering\",\n  \"license\": \"MIT\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/darkroomengineering/lenis.git\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/darkroomengineering/lenis/issues\"\n  },\n  \"homepage\": \"https://github.com/darkroomengineering/lenis\",\n  \"funding\": {\n    \"type\": \"github\",\n    \"url\": \"https://github.com/sponsors/darkroomengineering\"\n  },\n  \"keywords\": [\n    \"scroll\",\n    \"smooth\",\n    \"lenis\",\n    \"react\",\n    \"vue\"\n  ],\n  \"scripts\": {\n    \"build\": \"pnpm build:core && pnpm build:all\",\n    \"build:core\": \"tsup --config tsup.core.ts\",\n    \"build:all\": \"tsup\",\n    \"dev\": \"pnpm run -w --parallel /^dev:.*/\",\n    \"dev:build\": \"tsup --watch\",\n    \"dev:playground\": \"pnpm --filter playground dev\",\n    \"dev:nuxt\": \"pnpm --filter playground-nuxt dev\",\n    \"readme\": \"node ./scripts/update-readme.js\",\n    \"version:dev\": \"npm version prerelease --preid dev --force --no-git-tag-version\",\n    \"version:patch\": \"npm version patch --force --no-git-tag-version\",\n    \"version:minor\": \"npm version minor --force --no-git-tag-version\",\n    \"version:major\": \"npm version major --force --no-git-tag-version\",\n    \"postversion\": \"pnpm build && pnpm readme\",\n    \"publish:main\": \"npm publish\",\n    \"publish:dev\": \"npm publish --tag dev\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"devDependencies\": {\n    \"terser\": \"^5.37.0\",\n    \"tsup\": \"^8.3.5\",\n    \"typescript\": \"^5.7.3\"\n  },\n  \"peerDependencies\": {\n    \"react\": \">=17.0.0\",\n    \"vue\": \">=3.0.0\",\n    \"@nuxt/kit\": \">=3.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"react\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"@nuxt/kit\": {\n      \"optional\": true\n    }\n  },\n  \"unpkg\": \"./dist/lenis.mjs\",\n  \"main\": \"./dist/lenis.mjs\",\n  \"module\": \"./dist/lenis.mjs\",\n  \"types\": \"./dist/lenis.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/lenis.d.ts\",\n      \"default\": \"./dist/lenis.mjs\"\n    },\n    \"./react\": {\n      \"types\": \"./dist/lenis-react.d.ts\",\n      \"default\": \"./dist/lenis-react.mjs\"\n    },\n    \"./snap\": {\n      \"types\": \"./dist/lenis-snap.d.ts\",\n      \"default\": \"./dist/lenis-snap.mjs\"\n    },\n    \"./vue\": {\n      \"types\": \"./dist/lenis-vue.d.ts\",\n      \"default\": \"./dist/lenis-vue.mjs\"\n    },\n    \"./nuxt\": {\n      \"default\": \"./dist/lenis-vue-nuxt.mjs\"\n    },\n    \"./nuxt/runtime/*\": {\n      \"default\": \"./dist/nuxt/runtime/*.mjs\"\n    },\n    \"./dist/*\": \"./dist/*\"\n  }\n}\n", "/**\n * Clamp a value between a minimum and maximum value\n *\n * @param min Minimum value\n * @param input Value to clamp\n * @param max Maximum value\n * @returns Clamped value\n */\nexport function clamp(min: number, input: number, max: number) {\n  return Math.max(min, Math.min(input, max))\n}\n\n/**\n * Truncate a floating-point number to a specified number of decimal places\n *\n * @param value Value to truncate\n * @param decimals Number of decimal places to truncate to\n * @returns Truncated value\n */\nexport function truncate(value: number, decimals = 0) {\n  return parseFloat(value.toFixed(decimals))\n}\n\n/**\n *  Linearly interpolate between two values using an amount (0 <= t <= 1)\n *\n * @param x First value\n * @param y Second value\n * @param t Amount to interpolate (0 <= t <= 1)\n * @returns Interpolated value\n */\nexport function lerp(x: number, y: number, t: number) {\n  return (1 - t) * x + t * y\n}\n\n/**\n * Damp a value over time using a damping factor\n * {@link http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/}\n *\n * @param x Initial value\n * @param y Target value\n * @param lambda Damping factor\n * @param dt Time elapsed since the last update\n * @returns Damped value\n */\nexport function damp(x: number, y: number, lambda: number, deltaTime: number) {\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime))\n}\n\n/**\n * Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\n * {@link https://anguscroll.com/just/just-modulo}\n *\n * @param n Dividend\n * @param d Divisor\n * @returns Modulo\n */\nexport function modulo(n: number, d: number) {\n  return ((n % d) + d) % d\n}\n", "import { clamp, damp } from './maths'\r\nimport type { EasingFunction, FromToOptions, OnUpdateCallback } from './types'\r\n\r\n/**\r\n * Animate class to handle value animations with lerping or easing\r\n *\r\n * @example\r\n * const animate = new Animate()\r\n * animate.fromTo(0, 100, { duration: 1, easing: (t) => t })\r\n * animate.advance(0.5) // 50\r\n */\r\nexport class Animate {\r\n  isRunning = false\r\n  value = 0\r\n  from = 0\r\n  to = 0\r\n  currentTime = 0\r\n\r\n  // These are instanciated in the fromTo method\r\n  lerp?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  onUpdate?: OnUpdateCallback\r\n\r\n  /**\r\n   * Advance the animation by the given delta time\r\n   *\r\n   * @param deltaTime - The time in seconds to advance the animation\r\n   */\r\n  advance(deltaTime: number) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.duration && this.easing) {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    } else if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      // If no easing or lerp, just jump to the end value\r\n      this.value = this.to\r\n      completed = true\r\n    }\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n  }\r\n\r\n  /** Stop the animation */\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  /**\r\n   * Set up the animation from a starting value to an ending value\r\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\r\n   *\r\n   * @param from - The starting value\r\n   * @param to - The ending value\r\n   * @param options - Options for the animation\r\n   */\r\n  fromTo(\r\n    from: number,\r\n    to: number,\r\n    { lerp, duration, easing, onStart, onUpdate }: FromToOptions\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "export function debounce<CB extends (...args: any[]) => void>(\n  callback: CB,\n  delay: number\n) {\n  let timer: number | undefined\n  return function <T>(this: T, ...args: Parameters<typeof callback>) {\n    let context = this\n    clearTimeout(timer)\n    timer = setTimeout(() => {\n      timer = undefined\n      callback.apply(context, args)\n    }, delay)\n  }\n}\n", "import { debounce } from './debounce'\n\n/**\n * Dimensions class to handle the size of the content and wrapper\n *\n * @example\n * const dimensions = new Dimensions(wrapper, content)\n * dimensions.on('resize', (e) => {\n *   console.log(e.width, e.height)\n * })\n */\nexport class Dimensions {\n  width = 0\n  height = 0\n  scrollHeight = 0\n  scrollWidth = 0\n\n  // These are instanciated in the constructor as they need information from the options\n  private debouncedResize?: (...args: unknown[]) => void\n  private wrapperResizeObserver?: ResizeObserver\n  private contentResizeObserver?: ResizeObserver\n\n  constructor(\n    private wrapper: HTMLElement | Window | Element,\n    private content: HTMLElement | Element,\n    { autoResize = true, debounce: debounceValue = 250 } = {}\n  ) {\n    if (autoResize) {\n      this.debouncedResize = debounce(this.resize, debounceValue)\n\n      if (this.wrapper instanceof Window) {\n        window.addEventListener('resize', this.debouncedResize, false)\n      } else {\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\n        this.wrapperResizeObserver.observe(this.wrapper)\n      }\n\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\n      this.contentResizeObserver.observe(this.content)\n    }\n\n    this.resize()\n  }\n\n  destroy() {\n    this.wrapperResizeObserver?.disconnect()\n    this.contentResizeObserver?.disconnect()\n\n    if (this.wrapper === window && this.debouncedResize) {\n      window.removeEventListener('resize', this.debouncedResize, false)\n    }\n  }\n\n  resize = () => {\n    this.onWrapperResize()\n    this.onContentResize()\n  }\n\n  onWrapperResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.width = window.innerWidth\n      this.height = window.innerHeight\n    } else {\n      this.width = this.wrapper.clientWidth\n      this.height = this.wrapper.clientHeight\n    }\n  }\n\n  onContentResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.scrollHeight = this.content.scrollHeight\n      this.scrollWidth = this.content.scrollWidth\n    } else {\n      this.scrollHeight = this.wrapper.scrollHeight\n      this.scrollWidth = this.wrapper.scrollWidth\n    }\n  }\n\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height,\n    }\n  }\n}\n", "/**\n * Emitter class to handle events\n * @example\n * const emitter = new Emitter()\n * emitter.on('event', (data) => {\n *   console.log(data)\n * })\n * emitter.emit('event', 'data')\n */\nexport class Emitter {\n  private events: Record<\n    string,\n    Array<(...args: unknown[]) => void> | undefined\n  > = {}\n\n  /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */\n  emit(event: string, ...args: unknown[]) {\n    let callbacks = this.events[event] || []\n    for (let i = 0, length = callbacks.length; i < length; i++) {\n      callbacks[i]?.(...args)\n    }\n  }\n\n  /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */\n  on<CB extends (...args: any[]) => void>(event: string, cb: CB) {\n    // Add the callback to the event's callback list, or create a new list with the callback\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\n\n    // Return an unsubscribe function\n    return () => {\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\n    }\n  }\n\n  /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off<CB extends (...args: any[]) => void>(event: string, callback: CB) {\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\n  }\n\n  /**\n   * Remove all event listeners and clean up\n   */\n  destroy() {\n    this.events = {}\n  }\n}\n", "import { Emitter } from './emitter'\r\nimport type { VirtualScrollCallback } from './types'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\nconst listenerOptions: AddEventListenerOptions = { passive: false }\r\n\r\nexport class VirtualScroll {\r\n  touchStart = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  lastDelta = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  window = {\r\n    width: 0,\r\n    height: 0,\r\n  }\r\n  private emitter = new Emitter()\r\n\r\n  constructor(\r\n    private element: HTMLElement,\r\n    private options = { wheelMultiplier: 1, touchMultiplier: 1 }\r\n  ) {\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.addEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener('touchend', this.onTouchEnd, listenerOptions)\r\n  }\r\n\r\n  /**\r\n   * Add an event listener for the given event and callback\r\n   *\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  on(event: string, callback: VirtualScrollCallback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  /** Remove all event listeners and clean up */\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.removeEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchend',\r\n      this.onTouchEnd,\r\n      listenerOptions\r\n    )\r\n  }\r\n\r\n  /**\r\n   * Event handler for 'touchstart' event\r\n   *\r\n   * @param event Touch event\r\n   */\r\n  onTouchStart = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'touchmove' event */\r\n  onTouchMove = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event: TouchEvent) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'wheel' event */\r\n  onWheel = (event: WheelEvent) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.options.wheelMultiplier\r\n    deltaY *= this.options.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.window = {\r\n      width: window.innerWidth,\r\n      height: window.innerHeight,\r\n    }\r\n  }\r\n}\r\n", "import { version } from '../../../package.json'\nimport { Animate } from './animate'\nimport { Dimensions } from './dimensions'\nimport { Emitter } from './emitter'\nimport { clamp, modulo } from './maths'\nimport type {\n  LenisEvent,\n  LenisOptions,\n  ScrollCallback,\n  Scrolling,\n  ScrollToOptions,\n  UserData,\n  VirtualScrollCallback,\n  VirtualScrollData,\n} from './types'\nimport { VirtualScroll } from './virtual-scroll'\n\n// Technical explanation\n// - listen to 'wheel' events\n// - prevent 'wheel' event to prevent scroll\n// - normalize wheel delta\n// - add delta to targetScroll\n// - animate scroll to targetScroll (smooth context)\n// - if animation is not running, listen to 'scroll' events (native context)\n\ntype OptionalPick<T, F extends keyof T> = Omit<T, F> & Partial<Pick<T, F>>\n\nconst defaultEasing = (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t))\n\nexport class Lenis {\n  private _isScrolling: Scrolling = false // true when scroll is animating\n  private _isStopped = false // true if user should not be able to scroll - enable/disable programmatically\n  private _isLocked = false // same as isStopped but enabled/disabled when scroll reaches target\n  private _preventNextNativeScrollEvent = false\n  private _resetVelocityTimeout: ReturnType<typeof setTimeout> | null = null\n  private __rafID: number | null = null\n\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching?: boolean\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData: UserData = {}\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0\n  /**\n   * The direction of the scroll\n   */\n  direction: 1 | -1 | 0 = 0\n  /**\n   * The options passed to the lenis instance\n   */\n  options: OptionalPick<\n    Required<LenisOptions>,\n    'duration' | 'easing' | 'prevent' | 'virtualScroll'\n  >\n  /**\n   * The target scroll value\n   */\n  targetScroll: number\n  /**\n   * The animated scroll value\n   */\n  animatedScroll: number\n\n  // These are instanciated here as they don't need information from the options\n  private readonly animate = new Animate()\n  private readonly emitter = new Emitter()\n  // These are instanciated in the constructor as they need information from the options\n  readonly dimensions: Dimensions // This is not private because it's used in the Snap class\n  private readonly virtualScroll: VirtualScroll\n\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaMultiplier = 35,\n    duration, // in seconds\n    easing,\n    lerp = 0.1,\n    infinite = false,\n    orientation = 'vertical', // vertical, horizontal\n    gestureOrientation = 'vertical', // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false, // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false,\n  }: LenisOptions = {}) {\n    // Set version\n    window.lenisVersion = version\n\n    // Check if wrapper is <html>, fallback to window\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    // Setup options\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaMultiplier,\n      duration,\n      easing,\n      lerp,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions,\n    }\n\n    // Setup dimensions instance\n    this.dimensions = new Dimensions(wrapper, content, { autoResize })\n\n    // Setup class name\n    this.updateClassName()\n\n    // Set the initial scroll value for all scroll information\n    this.targetScroll = this.animatedScroll = this.actualScroll\n\n    // Add event listeners\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\n\n    this.options.wrapper.addEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.options.wrapper.addEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    // Setup virtual scroll instance\n    this.virtualScroll = new VirtualScroll(eventsTarget as HTMLElement, {\n      touchMultiplier,\n      wheelMultiplier,\n    })\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\n\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener('transitionend', this.onTransitionEnd, {\n        passive: true,\n      })\n    }\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy()\n\n    this.options.wrapper.removeEventListener(\n      'scroll',\n      this.onNativeScroll,\n      false\n    )\n\n    this.options.wrapper.removeEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    this.options.wrapper.removeEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.virtualScroll.destroy()\n    this.dimensions.destroy()\n\n    this.cleanUpClassName()\n\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID)\n    }\n  }\n\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   * @returns Unsubscribe function\n   */\n  on(event: 'scroll', callback: ScrollCallback): () => void\n  on(event: 'virtual-scroll', callback: VirtualScrollCallback): () => void\n  on(event: LenisEvent, callback: any) {\n    return this.emitter.on(event, callback)\n  }\n\n  /**\n   * Remove an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event: 'scroll', callback: ScrollCallback): void\n  off(event: 'virtual-scroll', callback: VirtualScrollCallback): void\n  off(event: LenisEvent, callback: any) {\n    return this.emitter.off(event, callback)\n  }\n\n  private onScrollEnd = (e: Event | CustomEvent) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === 'smooth' || this.isScrolling === false) {\n        e.stopPropagation()\n      }\n    }\n  }\n\n  private dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent('scrollend', {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true,\n        },\n      })\n    )\n  }\n\n  private onTransitionEnd = (event: TransitionEvent) => {\n    if (event.propertyName.includes('overflow')) {\n      const property = this.isHorizontal ? 'overflow-x' : 'overflow-y'\n\n      const overflow = getComputedStyle(this.rootElement)[\n        property as keyof CSSStyleDeclaration\n      ] as string\n\n      if (['hidden', 'clip'].includes(overflow)) {\n        this.stop()\n      } else {\n        this.start()\n      }\n    }\n  }\n\n  private setScroll(scroll: number) {\n    // behavior: 'instant' bypasses the scroll-behavior CSS property\n\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: 'instant' })\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: 'instant' })\n    }\n  }\n\n  private onClick = (event: PointerEvent | MouseEvent) => {\n    const path = event.composedPath()\n    const anchor = path.find(\n      (node) =>\n        node instanceof HTMLAnchorElement &&\n        (node.getAttribute('href')?.startsWith('#') ||\n          node.getAttribute('href')?.startsWith('/#') ||\n          node.getAttribute('href')?.startsWith('./#'))\n    ) as HTMLAnchorElement | undefined\n    if (anchor) {\n      const id = anchor.getAttribute('href')\n\n      if (id) {\n        const options =\n          typeof this.options.anchors === 'object' && this.options.anchors\n            ? this.options.anchors\n            : undefined\n\n        let target: number | string = `#${id.split('#')[1]}`\n        if (['#', '/#', './#', '#top', '/#top', './#top'].includes(id)) {\n          target = 0\n        }\n\n        this.scrollTo(target, options)\n      }\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent | MouseEvent) => {\n    if (event.button === 1) {\n      this.reset()\n    }\n  }\n\n  private onVirtualScroll = (data: VirtualScrollData) => {\n    if (\n      typeof this.options.virtualScroll === 'function' &&\n      this.options.virtualScroll(data) === false\n    )\n      return\n\n    const { deltaX, deltaY, event } = data\n\n    this.emitter.emit('virtual-scroll', { deltaX, deltaY, event })\n\n    // keep zoom feature\n    if (event.ctrlKey) return\n    // @ts-ignore\n    if (event.lenisStopPropagation) return\n\n    const isTouch = event.type.includes('touch')\n    const isWheel = event.type.includes('wheel')\n\n    this.isTouching = event.type === 'touchstart' || event.type === 'touchmove'\n    // if (event.type === 'touchend') {\n    //   console.log('touchend', this.scroll)\n    //   // this.lastVelocity = this.velocity\n    //   // this.velocity = 0\n    //   // this.isScrolling = false\n    //   this.emit({ type: 'touchend' })\n    //   // alert('touchend')\n    //   return\n    // }\n\n    const isClickOrTap = deltaX === 0 && deltaY === 0\n\n    const isTapToStop =\n      this.options.syncTouch &&\n      isTouch &&\n      event.type === 'touchstart' &&\n      isClickOrTap &&\n      !this.isStopped &&\n      !this.isLocked\n\n    if (isTapToStop) {\n      this.reset()\n      return\n    }\n\n    // const isPullToRefresh =\n    //   this.options.gestureOrientation === 'vertical' &&\n    //   this.scroll === 0 &&\n    //   !this.options.infinite &&\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\n\n    const isUnknownGesture =\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\n\n    if (isClickOrTap || isUnknownGesture) {\n      // console.log('prevent')\n      return\n    }\n\n    // catch if scrolling on nested scroll elements\n    let composedPath = event.composedPath()\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\n\n    const prevent = this.options.prevent\n\n    if (\n      !!composedPath.find(\n        (node) =>\n          node instanceof HTMLElement &&\n          ((typeof prevent === 'function' && prevent?.(node)) ||\n            node.hasAttribute?.('data-lenis-prevent') ||\n            (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\n            (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')) ||\n            (this.options.allowNestedScroll &&\n              this.checkNestedScroll(node, { deltaX, deltaY })))\n      )\n    )\n      return\n\n    if (this.isStopped || this.isLocked) {\n      event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\n      return\n    }\n\n    const isSmooth =\n      (this.options.syncTouch && isTouch) ||\n      (this.options.smoothWheel && isWheel)\n\n    if (!isSmooth) {\n      this.isScrolling = 'native'\n      this.animate.stop()\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      return\n    }\n\n    let delta = deltaY\n    if (this.options.gestureOrientation === 'both') {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\n    } else if (this.options.gestureOrientation === 'horizontal') {\n      delta = deltaX\n    }\n\n    if (\n      !this.options.overscroll ||\n      this.options.infinite ||\n      (this.options.wrapper !== window &&\n        ((this.animatedScroll > 0 && this.animatedScroll < this.limit) ||\n          (this.animatedScroll === 0 && deltaY > 0) ||\n          (this.animatedScroll === this.limit && deltaY < 0)))\n    ) {\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      // event.stopPropagation()\n    }\n\n    event.preventDefault()\n\n    const isSyncTouch = isTouch && this.options.syncTouch\n    const isTouchEnd = isTouch && event.type === 'touchend'\n\n    const hasTouchInertia = isTouchEnd && Math.abs(delta) > 5\n\n    if (hasTouchInertia) {\n      delta = this.velocity * this.options.touchInertiaMultiplier\n    }\n\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...(isSyncTouch\n        ? {\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\n            // immediate: !hasTouchInertia,\n          }\n        : {\n            lerp: this.options.lerp,\n            duration: this.options.duration,\n            easing: this.options.easing,\n          }),\n    })\n  }\n\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize()\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.emit()\n  }\n\n  private emit() {\n    this.emitter.emit('scroll', this)\n  }\n\n  private onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout)\n      this._resetVelocityTimeout = null\n    }\n\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false\n      return\n    }\n\n    if (this.isScrolling === false || this.isScrolling === 'native') {\n      const lastScroll = this.animatedScroll\n      this.animatedScroll = this.targetScroll = this.actualScroll\n      this.lastVelocity = this.velocity\n      this.velocity = this.animatedScroll - lastScroll\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      ) as Lenis['direction']\n\n      if (!this.isStopped) {\n        this.isScrolling = 'native'\n      }\n\n      this.emit()\n\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity\n          this.velocity = 0\n          this.isScrolling = false\n          this.emit()\n        }, 400)\n      }\n    }\n  }\n\n  private reset() {\n    this.isLocked = false\n    this.isScrolling = false\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.lastVelocity = this.velocity = 0\n    this.animate.stop()\n  }\n\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return\n    this.reset()\n\n    this.isStopped = false\n\n    this.emit()\n  }\n\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return\n    this.reset()\n\n    this.isStopped = true\n\n    this.emit()\n  }\n\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time: number) => {\n    const deltaTime = time - (this.time || time)\n    this.time = time\n\n    this.animate.advance(deltaTime * 0.001)\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(\n    target: number | string | HTMLElement,\n    {\n      offset = 0,\n      immediate = false,\n      lock = false,\n      duration = this.options.duration,\n      easing = this.options.easing,\n      lerp = this.options.lerp,\n      onStart,\n      onComplete,\n      force = false, // scroll even if stopped\n      programmatic = true, // called from outside of the class\n      userData,\n    }: ScrollToOptions = {}\n  ) {\n    if ((this.isStopped || this.isLocked) && !force) return\n\n    // keywords\n    if (\n      typeof target === 'string' &&\n      ['top', 'left', 'start'].includes(target)\n    ) {\n      target = 0\n    } else if (\n      typeof target === 'string' &&\n      ['bottom', 'right', 'end'].includes(target)\n    ) {\n      target = this.limit\n    } else {\n      let node\n\n      if (typeof target === 'string') {\n        // CSS selector\n        node = document.querySelector(target)\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        // Node element\n        node = target\n      }\n\n      if (node) {\n        if (this.options.wrapper !== window) {\n          // nested scroll offset correction\n          const wrapperRect = this.rootElement.getBoundingClientRect()\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\n        }\n\n        const rect = node.getBoundingClientRect()\n\n        target =\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\n      }\n    }\n\n    if (typeof target !== 'number') return\n\n    target += offset\n    target = Math.round(target)\n\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll\n\n        const distance = target - this.animatedScroll\n\n        if (distance > this.limit / 2) {\n          target = target - this.limit\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit)\n    }\n\n    if (target === this.targetScroll) {\n      onStart?.(this)\n      onComplete?.(this)\n      return\n    }\n\n    this.userData = userData ?? {}\n\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target\n      this.setScroll(this.scroll)\n      this.reset()\n      this.preventNextNativeScrollEvent()\n      this.emit()\n      onComplete?.(this)\n      this.userData = {}\n\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent()\n      })\n      return\n    }\n\n    if (!programmatic) {\n      this.targetScroll = target\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp,\n      onStart: () => {\n        // started\n        if (lock) this.isLocked = true\n        this.isScrolling = 'smooth'\n        onStart?.(this)\n      },\n      onUpdate: (value: number, completed: boolean) => {\n        this.isScrolling = 'smooth'\n\n        // updated\n        this.lastVelocity = this.velocity\n        this.velocity = value - this.animatedScroll\n        this.direction = Math.sign(this.velocity) as Lenis['direction']\n\n        this.animatedScroll = value\n        this.setScroll(this.scroll)\n\n        if (programmatic) {\n          // wheel during programmatic should stop it\n          this.targetScroll = value\n        }\n\n        if (!completed) this.emit()\n\n        if (completed) {\n          this.reset()\n          this.emit()\n          onComplete?.(this)\n          this.userData = {}\n\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent()\n          })\n\n          // avoid emitting event twice\n          this.preventNextNativeScrollEvent()\n        }\n      },\n    })\n  }\n\n  private preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true\n\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false\n    })\n  }\n\n  private checkNestedScroll(\n    node: HTMLElement,\n    { deltaX, deltaY }: { deltaX: number; deltaY: number }\n  ) {\n    const time = Date.now()\n\n    // @ts-ignore\n    const cache = (node._lenis ??= {})\n\n    let hasOverflowX,\n      hasOverflowY,\n      isScrollableX,\n      isScrollableY,\n      scrollWidth,\n      scrollHeight,\n      clientWidth,\n      clientHeight\n\n    const gestureOrientation = this.options.gestureOrientation\n\n    if (time - (cache.time ?? 0) > 2000) {\n      cache.time = Date.now()\n\n      const computedStyle = window.getComputedStyle(node)\n      cache.computedStyle = computedStyle\n\n      const overflowXString = computedStyle.overflowX\n      const overflowYString = computedStyle.overflowY\n\n      hasOverflowX = ['auto', 'overlay', 'scroll'].includes(overflowXString)\n      hasOverflowY = ['auto', 'overlay', 'scroll'].includes(overflowYString)\n      cache.hasOverflowX = hasOverflowX\n      cache.hasOverflowY = hasOverflowY\n\n      if (!hasOverflowX && !hasOverflowY) return false // if no overflow, it's not scrollable no matter what, early return saves some computations\n      if (gestureOrientation === 'vertical' && !hasOverflowY) return false\n      if (gestureOrientation === 'horizontal' && !hasOverflowX) return false\n\n      scrollWidth = node.scrollWidth\n      scrollHeight = node.scrollHeight\n\n      clientWidth = node.clientWidth\n      clientHeight = node.clientHeight\n\n      isScrollableX = scrollWidth > clientWidth\n      isScrollableY = scrollHeight > clientHeight\n\n      cache.isScrollableX = isScrollableX\n      cache.isScrollableY = isScrollableY\n      cache.scrollWidth = scrollWidth\n      cache.scrollHeight = scrollHeight\n      cache.clientWidth = clientWidth\n      cache.clientHeight = clientHeight\n    } else {\n      isScrollableX = cache.isScrollableX\n      isScrollableY = cache.isScrollableY\n      hasOverflowX = cache.hasOverflowX\n      hasOverflowY = cache.hasOverflowY\n      scrollWidth = cache.scrollWidth\n      scrollHeight = cache.scrollHeight\n      clientWidth = cache.clientWidth\n      clientHeight = cache.clientHeight\n    }\n\n    if (\n      (!hasOverflowX && !hasOverflowY) ||\n      (!isScrollableX && !isScrollableY)\n    ) {\n      return false\n    }\n\n    if (gestureOrientation === 'vertical' && (!hasOverflowY || !isScrollableY))\n      return false\n\n    if (\n      gestureOrientation === 'horizontal' &&\n      (!hasOverflowX || !isScrollableX)\n    )\n      return false\n\n    let orientation: 'x' | 'y' | undefined\n\n    if (gestureOrientation === 'horizontal') {\n      orientation = 'x'\n    } else if (gestureOrientation === 'vertical') {\n      orientation = 'y'\n    } else {\n      const isScrollingX = deltaX !== 0\n      const isScrollingY = deltaY !== 0\n\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = 'x'\n      }\n\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = 'y'\n      }\n    }\n\n    if (!orientation) return false\n\n    let scroll, maxScroll, delta, hasOverflow, isScrollable\n\n    if (orientation === 'x') {\n      scroll = node.scrollLeft\n      maxScroll = scrollWidth - clientWidth\n      delta = deltaX\n\n      hasOverflow = hasOverflowX\n      isScrollable = isScrollableX\n    } else if (orientation === 'y') {\n      scroll = node.scrollTop\n      maxScroll = scrollHeight - clientHeight\n      delta = deltaY\n\n      hasOverflow = hasOverflowY\n      isScrollable = isScrollableY\n    } else {\n      return false\n    }\n\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0\n\n    return willScroll && hasOverflow && isScrollable\n  }\n\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return (\n      this.options.wrapper === window\n        ? document.documentElement\n        : this.options.wrapper\n    ) as HTMLElement\n  }\n\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\n    }\n  }\n\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === 'horizontal'\n  }\n\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    // value browser takes into account\n    // it has to be this way because of DOCTYPE declaration\n    const wrapper = this.options.wrapper as Window | HTMLElement\n\n    return this.isHorizontal\n      ? (wrapper as Window).scrollX ?? (wrapper as HTMLElement).scrollLeft\n      : (wrapper as Window).scrollY ?? (wrapper as HTMLElement).scrollTop\n  }\n\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite\n      ? modulo(this.animatedScroll, this.limit)\n      : this.animatedScroll\n  }\n\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    // avoid progress to be NaN\n    return this.limit === 0 ? 1 : this.scroll / this.limit\n  }\n\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling\n  }\n\n  private set isScrolling(value: Scrolling) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped\n  }\n\n  private set isStopped(value: boolean) {\n    if (this._isStopped !== value) {\n      this._isStopped = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked\n  }\n\n  private set isLocked(value: boolean) {\n    if (this._isLocked !== value) {\n      this._isLocked = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === 'smooth'\n  }\n\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = 'lenis'\n    if (this.options.autoToggle) className += ' lenis-autoToggle'\n    if (this.isStopped) className += ' lenis-stopped'\n    if (this.isLocked) className += ' lenis-locked'\n    if (this.isScrolling) className += ' lenis-scrolling'\n    if (this.isScrolling === 'smooth') className += ' lenis-smooth'\n    return className\n  }\n\n  private updateClassName() {\n    this.cleanUpClassName()\n\n    this.rootElement.className =\n      `${this.rootElement.className} ${this.className}`.trim()\n  }\n\n  private cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className\n      .replace(/lenis(-\\w+)?/g, '')\n      .trim()\n  }\n}\n"], "mappings": ";;;AAEE,IAAA,UAAW;ACMN,SAAS,MAAM,KAAa,OAAe,KAAa;AAC7D,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC;AAC3C;AAqBO,SAAS,KAAK,GAAW,GAAW,GAAW;AACpD,UAAQ,IAAI,KAAK,IAAI,IAAI;AAC3B;AAYO,SAAS,KAAK,GAAW,GAAW,QAAgB,WAAmB;AAC5E,SAAO,KAAK,GAAG,GAAG,IAAI,KAAK,IAAI,CAAC,SAAS,SAAS,CAAC;AACrD;AAUO,SAAS,OAAO,GAAW,GAAW;AAC3C,UAAS,IAAI,IAAK,KAAK;AACzB;AChDO,IAAM,UAAN,MAAc;EACnB,YAAY;EACZ,QAAQ;EACR,OAAO;EACP,KAAK;EACL,cAAc;;EAGd;EACA;EACA;EACA;;;;;;EAOA,QAAQ,WAAmB;AACzB,QAAI,CAAC,KAAK,UAAW;AAErB,QAAI,YAAY;AAEhB,QAAI,KAAK,YAAY,KAAK,QAAQ;AAChC,WAAK,eAAe;AACpB,YAAM,iBAAiB,MAAM,GAAG,KAAK,cAAc,KAAK,UAAU,CAAC;AAEnE,kBAAY,kBAAkB;AAC9B,YAAM,gBAAgB,YAAY,IAAI,KAAK,OAAO,cAAc;AAChE,WAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ;IACnD,WAAW,KAAK,MAAM;AACpB,WAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,SAAS;AAChE,UAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI;AACtC,aAAK,QAAQ,KAAK;AAClB,oBAAY;MACd;IACF,OAAO;AAEL,WAAK,QAAQ,KAAK;AAClB,kBAAY;IACd;AAEA,QAAI,WAAW;AACb,WAAK,KAAK;IACZ;AAGA,SAAK,WAAW,KAAK,OAAO,SAAS;EACvC;;EAGA,OAAO;AACL,SAAK,YAAY;EACnB;;;;;;;;;EAUA,OACE,MACA,IACA,EAAE,MAAAA,OAAM,UAAU,QAAQ,SAAS,SAAS,GAC5C;AACA,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,KAAK;AACV,SAAK,OAAOA;AACZ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,YAAY;AAEjB,cAAU;AACV,SAAK,WAAW;EAClB;AACF;AC1FO,SAAS,SACd,UACA,OACA;AACA,MAAI;AACJ,SAAO,YAAyB,MAAmC;AACjE,QAAI,UAAU;AACd,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM;AACvB,cAAQ;AACR,eAAS,MAAM,SAAS,IAAI;IAC9B,GAAG,KAAK;EACV;AACF;ACFO,IAAM,aAAN,MAAiB;EAWtB,YACU,SACA,SACR,EAAE,aAAa,MAAM,UAAU,gBAAgB,IAAI,IAAI,CAAC,GACxD;AAHQ,SAAA,UAAA;AACA,SAAA,UAAA;AAGR,QAAI,YAAY;AACd,WAAK,kBAAkB,SAAS,KAAK,QAAQ,aAAa;AAE1D,UAAI,KAAK,mBAAmB,QAAQ;AAClC,eAAO,iBAAiB,UAAU,KAAK,iBAAiB,KAAK;MAC/D,OAAO;AACL,aAAK,wBAAwB,IAAI,eAAe,KAAK,eAAe;AACpE,aAAK,sBAAsB,QAAQ,KAAK,OAAO;MACjD;AAEA,WAAK,wBAAwB,IAAI,eAAe,KAAK,eAAe;AACpE,WAAK,sBAAsB,QAAQ,KAAK,OAAO;IACjD;AAEA,SAAK,OAAO;EACd;EA9BA,QAAQ;EACR,SAAS;EACT,eAAe;EACf,cAAc;;EAGN;EACA;EACA;EAwBR,UAAU;AACR,SAAK,uBAAuB,WAAW;AACvC,SAAK,uBAAuB,WAAW;AAEvC,QAAI,KAAK,YAAY,UAAU,KAAK,iBAAiB;AACnD,aAAO,oBAAoB,UAAU,KAAK,iBAAiB,KAAK;IAClE;EACF;EAEA,SAAS,MAAM;AACb,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;EACvB;EAEA,kBAAkB,MAAM;AACtB,QAAI,KAAK,mBAAmB,QAAQ;AAClC,WAAK,QAAQ,OAAO;AACpB,WAAK,SAAS,OAAO;IACvB,OAAO;AACL,WAAK,QAAQ,KAAK,QAAQ;AAC1B,WAAK,SAAS,KAAK,QAAQ;IAC7B;EACF;EAEA,kBAAkB,MAAM;AACtB,QAAI,KAAK,mBAAmB,QAAQ;AAClC,WAAK,eAAe,KAAK,QAAQ;AACjC,WAAK,cAAc,KAAK,QAAQ;IAClC,OAAO;AACL,WAAK,eAAe,KAAK,QAAQ;AACjC,WAAK,cAAc,KAAK,QAAQ;IAClC;EACF;EAEA,IAAI,QAAQ;AACV,WAAO;MACL,GAAG,KAAK,cAAc,KAAK;MAC3B,GAAG,KAAK,eAAe,KAAK;IAC9B;EACF;AACF;AC3EO,IAAM,UAAN,MAAc;EACX,SAGJ,CAAC;;;;;;EAOL,KAAK,UAAkB,MAAiB;AACtC,QAAI,YAAY,KAAK,OAAO,KAAK,KAAK,CAAC;AACvC,aAAS,IAAI,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AAC1D,gBAAU,CAAC,IAAI,GAAG,IAAI;IACxB;EACF;;;;;;;EAQA,GAAwC,OAAe,IAAQ;AAE7D,SAAK,OAAO,KAAK,GAAG,KAAK,EAAE,MAAM,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE;AAGzD,WAAO,MAAM;AACX,WAAK,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC;IACjE;EACF;;;;;;EAOA,IAAyC,OAAe,UAAc;AACpE,SAAK,OAAO,KAAK,IAAI,KAAK,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,aAAa,CAAC;EACvE;;;;EAKA,UAAU;AACR,SAAK,SAAS,CAAC;EACjB;AACF;ACvDA,IAAM,cAAc,MAAM;AAC1B,IAAM,kBAA2C,EAAE,SAAS,MAAM;AAE3D,IAAM,gBAAN,MAAoB;EAezB,YACU,SACA,UAAU,EAAE,iBAAiB,GAAG,iBAAiB,EAAE,GAC3D;AAFQ,SAAA,UAAA;AACA,SAAA,UAAA;AAER,WAAO,iBAAiB,UAAU,KAAK,gBAAgB,KAAK;AAC5D,SAAK,eAAe;AAEpB,SAAK,QAAQ,iBAAiB,SAAS,KAAK,SAAS,eAAe;AACpE,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ,iBAAiB,YAAY,KAAK,YAAY,eAAe;EAC5E;EAjCA,aAAa;IACX,GAAG;IACH,GAAG;EACL;EACA,YAAY;IACV,GAAG;IACH,GAAG;EACL;EACA,SAAS;IACP,OAAO;IACP,QAAQ;EACV;EACQ,UAAU,IAAI,QAAQ;;;;;;;EA6B9B,GAAG,OAAe,UAAiC;AACjD,WAAO,KAAK,QAAQ,GAAG,OAAO,QAAQ;EACxC;;EAGA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAErB,WAAO,oBAAoB,UAAU,KAAK,gBAAgB,KAAK;AAE/D,SAAK,QAAQ,oBAAoB,SAAS,KAAK,SAAS,eAAe;AACvE,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;AACA,SAAK,QAAQ;MACX;MACA,KAAK;MACL;IACF;EACF;;;;;;EAOA,eAAe,CAAC,UAAsB;AAEpC,UAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,gBAC/B,MAAM,cAAc,CAAC,IACrB;AAEJ,SAAK,WAAW,IAAI;AACpB,SAAK,WAAW,IAAI;AAEpB,SAAK,YAAY;MACf,GAAG;MACH,GAAG;IACL;AAEA,SAAK,QAAQ,KAAK,UAAU;MAC1B,QAAQ;MACR,QAAQ;MACR;IACF,CAAC;EACH;;EAGA,cAAc,CAAC,UAAsB;AAEnC,UAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,gBAC/B,MAAM,cAAc,CAAC,IACrB;AAEJ,UAAM,SAAS,EAAE,UAAU,KAAK,WAAW,KAAK,KAAK,QAAQ;AAC7D,UAAM,SAAS,EAAE,UAAU,KAAK,WAAW,KAAK,KAAK,QAAQ;AAE7D,SAAK,WAAW,IAAI;AACpB,SAAK,WAAW,IAAI;AAEpB,SAAK,YAAY;MACf,GAAG;MACH,GAAG;IACL;AAEA,SAAK,QAAQ,KAAK,UAAU;MAC1B;MACA;MACA;IACF,CAAC;EACH;EAEA,aAAa,CAAC,UAAsB;AAClC,SAAK,QAAQ,KAAK,UAAU;MAC1B,QAAQ,KAAK,UAAU;MACvB,QAAQ,KAAK,UAAU;MACvB;IACF,CAAC;EACH;;EAGA,UAAU,CAAC,UAAsB;AAC/B,QAAI,EAAE,QAAQ,QAAQ,UAAU,IAAI;AAEpC,UAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,KAAK,OAAO,QAAQ;AACxE,UAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,KAAK,OAAO,SAAS;AAEzE,cAAU;AACV,cAAU;AAEV,cAAU,KAAK,QAAQ;AACvB,cAAU,KAAK,QAAQ;AAEvB,SAAK,QAAQ,KAAK,UAAU,EAAE,QAAQ,QAAQ,MAAM,CAAC;EACvD;EAEA,iBAAiB,MAAM;AACrB,SAAK,SAAS;MACZ,OAAO,OAAO;MACd,QAAQ,OAAO;IACjB;EACF;AACF;ACpIA,IAAM,gBAAgB,CAAC,MAAc,KAAK,IAAI,GAAG,QAAQ,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC;AAEtE,IAAM,QAAN,MAAY;EACT,eAA0B;;EAC1B,aAAa;;EACb,YAAY;;EACZ,gCAAgC;EAChC,wBAA8D;EAC9D,UAAyB;;;;EAKjC;;;;EAIA,OAAO;;;;;;;;;;;EAWP,WAAqB,CAAC;;;;EAItB,eAAe;;;;EAIf,WAAW;;;;EAIX,YAAwB;;;;EAIxB;;;;EAOA;;;;EAIA;;EAGiB,UAAU,IAAI,QAAQ;EACtB,UAAU,IAAI,QAAQ;;EAE9B;;EACQ;EAEjB,YAAY;IACV,UAAU;IACV,UAAU,SAAS;IACnB,eAAe;IACf,cAAc;IACd,YAAY;IACZ,gBAAgB;IAChB,yBAAyB;IACzB;;IACA;IACA,MAAAA,QAAO;IACP,WAAW;IACX,cAAc;;IACd,qBAAqB;;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb;IACA;IACA,aAAa;IACb,UAAU;IACV,UAAU;IACV,aAAa;;IACb,oBAAoB;IACpB,kCAAkC;EACpC,IAAkB,CAAC,GAAG;AAEpB,WAAO,eAAe;AAGtB,QAAI,CAAC,WAAW,YAAY,SAAS,iBAAiB;AACpD,gBAAU;IACZ;AAGA,QAAI,OAAO,aAAa,YAAY,OAAO,WAAW,YAAY;AAChE,eAAS;IACX,WAAW,OAAO,WAAW,cAAc,OAAO,aAAa,UAAU;AACvE,iBAAW;IACb;AAGA,SAAK,UAAU;MACb;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;AAGA,SAAK,aAAa,IAAI,WAAW,SAAS,SAAS,EAAE,WAAW,CAAC;AAGjE,SAAK,gBAAgB;AAGrB,SAAK,eAAe,KAAK,iBAAiB,KAAK;AAG/C,SAAK,QAAQ,QAAQ,iBAAiB,UAAU,KAAK,gBAAgB,KAAK;AAE1E,SAAK,QAAQ,QAAQ,iBAAiB,aAAa,KAAK,aAAa;MACnE,SAAS;IACX,CAAC;AAED,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,QAAQ;AAC3D,WAAK,QAAQ,QAAQ;QACnB;QACA,KAAK;QACL;MACF;IACF;AAEA,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAGA,SAAK,gBAAgB,IAAI,cAAc,cAA6B;MAClE;MACA;IACF,CAAC;AACD,SAAK,cAAc,GAAG,UAAU,KAAK,eAAe;AAEpD,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,YAAY,iBAAiB,iBAAiB,KAAK,iBAAiB;QACvE,SAAS;MACX,CAAC;IACH;AAEA,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,UAAU,sBAAsB,KAAK,GAAG;IAC/C;EACF;;;;EAKA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAErB,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAEA,SAAK,QAAQ,QAAQ,oBAAoB,aAAa,KAAK,aAAa;MACtE,SAAS;IACX,CAAC;AAED,SAAK,QAAQ,QAAQ;MACnB;MACA,KAAK;MACL;IACF;AAEA,QAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,QAAQ;AAC3D,WAAK,QAAQ,QAAQ;QACnB;QACA,KAAK;QACL;MACF;IACF;AAEA,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,QAAQ;AAExB,SAAK,iBAAiB;AAEtB,QAAI,KAAK,SAAS;AAChB,2BAAqB,KAAK,OAAO;IACnC;EACF;EAWA,GAAG,OAAmB,UAAe;AACnC,WAAO,KAAK,QAAQ,GAAG,OAAO,QAAQ;EACxC;EAUA,IAAI,OAAmB,UAAe;AACpC,WAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ;EACzC;EAEQ,cAAc,CAAC,MAA2B;AAChD,QAAI,EAAE,aAAa,cAAc;AAC/B,UAAI,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,OAAO;AAC/D,UAAE,gBAAgB;MACpB;IACF;EACF;EAEQ,yBAAyB,MAAM;AACrC,SAAK,QAAQ,QAAQ;MACnB,IAAI,YAAY,aAAa;QAC3B,SAAS,KAAK,QAAQ,YAAY;;QAElC,QAAQ;UACN,gBAAgB;QAClB;MACF,CAAC;IACH;EACF;EAEQ,kBAAkB,CAAC,UAA2B;AACpD,QAAI,MAAM,aAAa,SAAS,UAAU,GAAG;AAC3C,YAAM,WAAW,KAAK,eAAe,eAAe;AAEpD,YAAM,WAAW,iBAAiB,KAAK,WAAW,EAChD,QACF;AAEA,UAAI,CAAC,UAAU,MAAM,EAAE,SAAS,QAAQ,GAAG;AACzC,aAAK,KAAK;MACZ,OAAO;AACL,aAAK,MAAM;MACb;IACF;EACF;EAEQ,UAAU,QAAgB;AAGhC,QAAI,KAAK,cAAc;AACrB,WAAK,QAAQ,QAAQ,SAAS,EAAE,MAAM,QAAQ,UAAU,UAAU,CAAC;IACrE,OAAO;AACL,WAAK,QAAQ,QAAQ,SAAS,EAAE,KAAK,QAAQ,UAAU,UAAU,CAAC;IACpE;EACF;EAEQ,UAAU,CAAC,UAAqC;AACtD,UAAM,OAAO,MAAM,aAAa;AAChC,UAAM,SAAS,KAAK;MAClB,CAAC,SACC,gBAAgB,sBACf,KAAK,aAAa,MAAM,GAAG,WAAW,GAAG,KACxC,KAAK,aAAa,MAAM,GAAG,WAAW,IAAI,KAC1C,KAAK,aAAa,MAAM,GAAG,WAAW,KAAK;IACjD;AACA,QAAI,QAAQ;AACV,YAAM,KAAK,OAAO,aAAa,MAAM;AAErC,UAAI,IAAI;AACN,cAAM,UACJ,OAAO,KAAK,QAAQ,YAAY,YAAY,KAAK,QAAQ,UACrD,KAAK,QAAQ,UACb;AAEN,YAAI,SAA0B,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;AAClD,YAAI,CAAC,KAAK,MAAM,OAAO,QAAQ,SAAS,QAAQ,EAAE,SAAS,EAAE,GAAG;AAC9D,mBAAS;QACX;AAEA,aAAK,SAAS,QAAQ,OAAO;MAC/B;IACF;EACF;EAEQ,gBAAgB,CAAC,UAAqC;AAC5D,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,MAAM;IACb;EACF;EAEQ,kBAAkB,CAAC,SAA4B;AACrD,QACE,OAAO,KAAK,QAAQ,kBAAkB,cACtC,KAAK,QAAQ,cAAc,IAAI,MAAM;AAErC;AAEF,UAAM,EAAE,QAAQ,QAAQ,MAAM,IAAI;AAElC,SAAK,QAAQ,KAAK,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,CAAC;AAG7D,QAAI,MAAM,QAAS;AAEnB,QAAI,MAAM,qBAAsB;AAEhC,UAAM,UAAU,MAAM,KAAK,SAAS,OAAO;AAC3C,UAAM,UAAU,MAAM,KAAK,SAAS,OAAO;AAE3C,SAAK,aAAa,MAAM,SAAS,gBAAgB,MAAM,SAAS;AAWhE,UAAM,eAAe,WAAW,KAAK,WAAW;AAEhD,UAAM,cACJ,KAAK,QAAQ,aACb,WACA,MAAM,SAAS,gBACf,gBACA,CAAC,KAAK,aACN,CAAC,KAAK;AAER,QAAI,aAAa;AACf,WAAK,MAAM;AACX;IACF;AAQA,UAAM,mBACH,KAAK,QAAQ,uBAAuB,cAAc,WAAW,KAC7D,KAAK,QAAQ,uBAAuB,gBAAgB,WAAW;AAElE,QAAI,gBAAgB,kBAAkB;AAEpC;IACF;AAGA,QAAI,eAAe,MAAM,aAAa;AACtC,mBAAe,aAAa,MAAM,GAAG,aAAa,QAAQ,KAAK,WAAW,CAAC;AAE3E,UAAM,UAAU,KAAK,QAAQ;AAE7B,QACE,CAAC,CAAC,aAAa;MACb,CAAC,SACC,gBAAgB,gBACd,OAAO,YAAY,cAAc,UAAU,IAAI,KAC/C,KAAK,eAAe,oBAAoB,KACvC,WAAW,KAAK,eAAe,0BAA0B,KACzD,WAAW,KAAK,eAAe,0BAA0B,KACzD,KAAK,QAAQ,qBACZ,KAAK,kBAAkB,MAAM,EAAE,QAAQ,OAAO,CAAC;IACvD;AAEA;AAEF,QAAI,KAAK,aAAa,KAAK,UAAU;AACnC,YAAM,eAAe;AACrB;IACF;AAEA,UAAM,WACH,KAAK,QAAQ,aAAa,WAC1B,KAAK,QAAQ,eAAe;AAE/B,QAAI,CAAC,UAAU;AACb,WAAK,cAAc;AACnB,WAAK,QAAQ,KAAK;AAElB,YAAM,uBAAuB;AAC7B;IACF;AAEA,QAAI,QAAQ;AACZ,QAAI,KAAK,QAAQ,uBAAuB,QAAQ;AAC9C,cAAQ,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,SAAS;IACzD,WAAW,KAAK,QAAQ,uBAAuB,cAAc;AAC3D,cAAQ;IACV;AAEA,QACE,CAAC,KAAK,QAAQ,cACd,KAAK,QAAQ,YACZ,KAAK,QAAQ,YAAY,WACtB,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,SACrD,KAAK,mBAAmB,KAAK,SAAS,KACtC,KAAK,mBAAmB,KAAK,SAAS,SAAS,IACpD;AAEA,YAAM,uBAAuB;IAE/B;AAEA,UAAM,eAAe;AAErB,UAAM,cAAc,WAAW,KAAK,QAAQ;AAC5C,UAAM,aAAa,WAAW,MAAM,SAAS;AAE7C,UAAM,kBAAkB,cAAc,KAAK,IAAI,KAAK,IAAI;AAExD,QAAI,iBAAiB;AACnB,cAAQ,KAAK,WAAW,KAAK,QAAQ;IACvC;AAEA,SAAK,SAAS,KAAK,eAAe,OAAO;MACvC,cAAc;MACd,GAAI,cACA;QACE,MAAM,kBAAkB,KAAK,QAAQ,gBAAgB;;MAEvD,IACA;QACE,MAAM,KAAK,QAAQ;QACnB,UAAU,KAAK,QAAQ;QACvB,QAAQ,KAAK,QAAQ;MACvB;IACN,CAAC;EACH;;;;EAKA,SAAS;AACP,SAAK,WAAW,OAAO;AACvB,SAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,SAAK,KAAK;EACZ;EAEQ,OAAO;AACb,SAAK,QAAQ,KAAK,UAAU,IAAI;EAClC;EAEQ,iBAAiB,MAAM;AAC7B,QAAI,KAAK,0BAA0B,MAAM;AACvC,mBAAa,KAAK,qBAAqB;AACvC,WAAK,wBAAwB;IAC/B;AAEA,QAAI,KAAK,+BAA+B;AACtC,WAAK,gCAAgC;AACrC;IACF;AAEA,QAAI,KAAK,gBAAgB,SAAS,KAAK,gBAAgB,UAAU;AAC/D,YAAM,aAAa,KAAK;AACxB,WAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,WAAK,eAAe,KAAK;AACzB,WAAK,WAAW,KAAK,iBAAiB;AACtC,WAAK,YAAY,KAAK;QACpB,KAAK,iBAAiB;MACxB;AAEA,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,cAAc;MACrB;AAEA,WAAK,KAAK;AAEV,UAAI,KAAK,aAAa,GAAG;AACvB,aAAK,wBAAwB,WAAW,MAAM;AAC5C,eAAK,eAAe,KAAK;AACzB,eAAK,WAAW;AAChB,eAAK,cAAc;AACnB,eAAK,KAAK;QACZ,GAAG,GAAG;MACR;IACF;EACF;EAEQ,QAAQ;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,SAAK,eAAe,KAAK,WAAW;AACpC,SAAK,QAAQ,KAAK;EACpB;;;;EAKA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAW;AACrB,SAAK,MAAM;AAEX,SAAK,YAAY;AAEjB,SAAK,KAAK;EACZ;;;;EAKA,OAAO;AACL,QAAI,KAAK,UAAW;AACpB,SAAK,MAAM;AAEX,SAAK,YAAY;AAEjB,SAAK,KAAK;EACZ;;;;;;EAOA,MAAM,CAAC,SAAiB;AACtB,UAAM,YAAY,QAAQ,KAAK,QAAQ;AACvC,SAAK,OAAO;AAEZ,SAAK,QAAQ,QAAQ,YAAY,IAAK;AAEtC,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,UAAU,sBAAsB,KAAK,GAAG;IAC/C;EACF;;;;;;;;;;;;;;;;;;;;;EAsBA,SACE,QACA;IACE,SAAS;IACT,YAAY;IACZ,OAAO;IACP,WAAW,KAAK,QAAQ;IACxB,SAAS,KAAK,QAAQ;IACtB,MAAAA,QAAO,KAAK,QAAQ;IACpB;IACA;IACA,QAAQ;;IACR,eAAe;;IACf;EACF,IAAqB,CAAC,GACtB;AACA,SAAK,KAAK,aAAa,KAAK,aAAa,CAAC,MAAO;AAGjD,QACE,OAAO,WAAW,YAClB,CAAC,OAAO,QAAQ,OAAO,EAAE,SAAS,MAAM,GACxC;AACA,eAAS;IACX,WACE,OAAO,WAAW,YAClB,CAAC,UAAU,SAAS,KAAK,EAAE,SAAS,MAAM,GAC1C;AACA,eAAS,KAAK;IAChB,OAAO;AACL,UAAI;AAEJ,UAAI,OAAO,WAAW,UAAU;AAE9B,eAAO,SAAS,cAAc,MAAM;MACtC,WAAW,kBAAkB,eAAe,QAAQ,UAAU;AAE5D,eAAO;MACT;AAEA,UAAI,MAAM;AACR,YAAI,KAAK,QAAQ,YAAY,QAAQ;AAEnC,gBAAM,cAAc,KAAK,YAAY,sBAAsB;AAC3D,oBAAU,KAAK,eAAe,YAAY,OAAO,YAAY;QAC/D;AAEA,cAAM,OAAO,KAAK,sBAAsB;AAExC,kBACG,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,KAAK;MACtD;IACF;AAEA,QAAI,OAAO,WAAW,SAAU;AAEhC,cAAU;AACV,aAAS,KAAK,MAAM,MAAM;AAE1B,QAAI,KAAK,QAAQ,UAAU;AACzB,UAAI,cAAc;AAChB,aAAK,eAAe,KAAK,iBAAiB,KAAK;AAE/C,cAAM,WAAW,SAAS,KAAK;AAE/B,YAAI,WAAW,KAAK,QAAQ,GAAG;AAC7B,mBAAS,SAAS,KAAK;QACzB,WAAW,WAAW,CAAC,KAAK,QAAQ,GAAG;AACrC,mBAAS,SAAS,KAAK;QACzB;MACF;IACF,OAAO;AACL,eAAS,MAAM,GAAG,QAAQ,KAAK,KAAK;IACtC;AAEA,QAAI,WAAW,KAAK,cAAc;AAChC,gBAAU,IAAI;AACd,mBAAa,IAAI;AACjB;IACF;AAEA,SAAK,WAAW,YAAY,CAAC;AAE7B,QAAI,WAAW;AACb,WAAK,iBAAiB,KAAK,eAAe;AAC1C,WAAK,UAAU,KAAK,MAAM;AAC1B,WAAK,MAAM;AACX,WAAK,6BAA6B;AAClC,WAAK,KAAK;AACV,mBAAa,IAAI;AACjB,WAAK,WAAW,CAAC;AAEjB,4BAAsB,MAAM;AAC1B,aAAK,uBAAuB;MAC9B,CAAC;AACD;IACF;AAEA,QAAI,CAAC,cAAc;AACjB,WAAK,eAAe;IACtB;AAGA,QAAI,OAAO,aAAa,YAAY,OAAO,WAAW,YAAY;AAChE,eAAS;IACX,WAAW,OAAO,WAAW,cAAc,OAAO,aAAa,UAAU;AACvE,iBAAW;IACb;AAEA,SAAK,QAAQ,OAAO,KAAK,gBAAgB,QAAQ;MAC/C;MACA;MACA,MAAAA;MACA,SAAS,MAAM;AAEb,YAAI,KAAM,MAAK,WAAW;AAC1B,aAAK,cAAc;AACnB,kBAAU,IAAI;MAChB;MACA,UAAU,CAAC,OAAe,cAAuB;AAC/C,aAAK,cAAc;AAGnB,aAAK,eAAe,KAAK;AACzB,aAAK,WAAW,QAAQ,KAAK;AAC7B,aAAK,YAAY,KAAK,KAAK,KAAK,QAAQ;AAExC,aAAK,iBAAiB;AACtB,aAAK,UAAU,KAAK,MAAM;AAE1B,YAAI,cAAc;AAEhB,eAAK,eAAe;QACtB;AAEA,YAAI,CAAC,UAAW,MAAK,KAAK;AAE1B,YAAI,WAAW;AACb,eAAK,MAAM;AACX,eAAK,KAAK;AACV,uBAAa,IAAI;AACjB,eAAK,WAAW,CAAC;AAEjB,gCAAsB,MAAM;AAC1B,iBAAK,uBAAuB;UAC9B,CAAC;AAGD,eAAK,6BAA6B;QACpC;MACF;IACF,CAAC;EACH;EAEQ,+BAA+B;AACrC,SAAK,gCAAgC;AAErC,0BAAsB,MAAM;AAC1B,WAAK,gCAAgC;IACvC,CAAC;EACH;EAEQ,kBACN,MACA,EAAE,QAAQ,OAAO,GACjB;AACA,UAAM,OAAO,KAAK,IAAI;AAGtB,UAAM,QAAS,KAAK,WAAW,CAAC;AAEhC,QAAI,cACF,cACA,eACA,eACA,aACA,cACA,aACA;AAEF,UAAM,qBAAqB,KAAK,QAAQ;AAExC,QAAI,QAAQ,MAAM,QAAQ,KAAK,KAAM;AACnC,YAAM,OAAO,KAAK,IAAI;AAEtB,YAAM,gBAAgB,OAAO,iBAAiB,IAAI;AAClD,YAAM,gBAAgB;AAEtB,YAAM,kBAAkB,cAAc;AACtC,YAAM,kBAAkB,cAAc;AAEtC,qBAAe,CAAC,QAAQ,WAAW,QAAQ,EAAE,SAAS,eAAe;AACrE,qBAAe,CAAC,QAAQ,WAAW,QAAQ,EAAE,SAAS,eAAe;AACrE,YAAM,eAAe;AACrB,YAAM,eAAe;AAErB,UAAI,CAAC,gBAAgB,CAAC,aAAc,QAAO;AAC3C,UAAI,uBAAuB,cAAc,CAAC,aAAc,QAAO;AAC/D,UAAI,uBAAuB,gBAAgB,CAAC,aAAc,QAAO;AAEjE,oBAAc,KAAK;AACnB,qBAAe,KAAK;AAEpB,oBAAc,KAAK;AACnB,qBAAe,KAAK;AAEpB,sBAAgB,cAAc;AAC9B,sBAAgB,eAAe;AAE/B,YAAM,gBAAgB;AACtB,YAAM,gBAAgB;AACtB,YAAM,cAAc;AACpB,YAAM,eAAe;AACrB,YAAM,cAAc;AACpB,YAAM,eAAe;IACvB,OAAO;AACL,sBAAgB,MAAM;AACtB,sBAAgB,MAAM;AACtB,qBAAe,MAAM;AACrB,qBAAe,MAAM;AACrB,oBAAc,MAAM;AACpB,qBAAe,MAAM;AACrB,oBAAc,MAAM;AACpB,qBAAe,MAAM;IACvB;AAEA,QACG,CAAC,gBAAgB,CAAC,gBAClB,CAAC,iBAAiB,CAAC,eACpB;AACA,aAAO;IACT;AAEA,QAAI,uBAAuB,eAAe,CAAC,gBAAgB,CAAC;AAC1D,aAAO;AAET,QACE,uBAAuB,iBACtB,CAAC,gBAAgB,CAAC;AAEnB,aAAO;AAET,QAAI;AAEJ,QAAI,uBAAuB,cAAc;AACvC,oBAAc;IAChB,WAAW,uBAAuB,YAAY;AAC5C,oBAAc;IAChB,OAAO;AACL,YAAM,eAAe,WAAW;AAChC,YAAM,eAAe,WAAW;AAEhC,UAAI,gBAAgB,gBAAgB,eAAe;AACjD,sBAAc;MAChB;AAEA,UAAI,gBAAgB,gBAAgB,eAAe;AACjD,sBAAc;MAChB;IACF;AAEA,QAAI,CAAC,YAAa,QAAO;AAEzB,QAAI,QAAQ,WAAW,OAAO,aAAa;AAE3C,QAAI,gBAAgB,KAAK;AACvB,eAAS,KAAK;AACd,kBAAY,cAAc;AAC1B,cAAQ;AAER,oBAAc;AACd,qBAAe;IACjB,WAAW,gBAAgB,KAAK;AAC9B,eAAS,KAAK;AACd,kBAAY,eAAe;AAC3B,cAAQ;AAER,oBAAc;AACd,qBAAe;IACjB,OAAO;AACL,aAAO;IACT;AAEA,UAAM,aAAa,QAAQ,IAAI,SAAS,YAAY,SAAS;AAE7D,WAAO,cAAc,eAAe;EACtC;;;;EAKA,IAAI,cAAc;AAChB,WACE,KAAK,QAAQ,YAAY,SACrB,SAAS,kBACT,KAAK,QAAQ;EAErB;;;;EAKA,IAAI,QAAQ;AACV,QAAI,KAAK,QAAQ,iCAAiC;AAChD,UAAI,KAAK,cAAc;AACrB,eAAO,KAAK,YAAY,cAAc,KAAK,YAAY;MACzD,OAAO;AACL,eAAO,KAAK,YAAY,eAAe,KAAK,YAAY;MAC1D;IACF,OAAO;AACL,aAAO,KAAK,WAAW,MAAM,KAAK,eAAe,MAAM,GAAG;IAC5D;EACF;;;;EAKA,IAAI,eAAe;AACjB,WAAO,KAAK,QAAQ,gBAAgB;EACtC;;;;EAKA,IAAI,eAAe;AAGjB,UAAM,UAAU,KAAK,QAAQ;AAE7B,WAAO,KAAK,eACP,QAAmB,WAAY,QAAwB,aACvD,QAAmB,WAAY,QAAwB;EAC9D;;;;EAKA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,WAChB,OAAO,KAAK,gBAAgB,KAAK,KAAK,IACtC,KAAK;EACX;;;;EAKA,IAAI,WAAW;AAEb,WAAO,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS,KAAK;EACnD;;;;EAKA,IAAI,cAAc;AAChB,WAAO,KAAK;EACd;EAEA,IAAY,YAAY,OAAkB;AACxC,QAAI,KAAK,iBAAiB,OAAO;AAC/B,WAAK,eAAe;AACpB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,YAAY;AACd,WAAO,KAAK;EACd;EAEA,IAAY,UAAU,OAAgB;AACpC,QAAI,KAAK,eAAe,OAAO;AAC7B,WAAK,aAAa;AAClB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,WAAW;AACb,WAAO,KAAK;EACd;EAEA,IAAY,SAAS,OAAgB;AACnC,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,YAAY;AACjB,WAAK,gBAAgB;IACvB;EACF;;;;EAKA,IAAI,WAAW;AACb,WAAO,KAAK,gBAAgB;EAC9B;;;;EAKA,IAAI,YAAY;AACd,QAAI,YAAY;AAChB,QAAI,KAAK,QAAQ,WAAY,cAAa;AAC1C,QAAI,KAAK,UAAW,cAAa;AACjC,QAAI,KAAK,SAAU,cAAa;AAChC,QAAI,KAAK,YAAa,cAAa;AACnC,QAAI,KAAK,gBAAgB,SAAU,cAAa;AAChD,WAAO;EACT;EAEQ,kBAAkB;AACxB,SAAK,iBAAiB;AAEtB,SAAK,YAAY,YACf,GAAG,KAAK,YAAY,SAAS,IAAI,KAAK,SAAS,GAAG,KAAK;EAC3D;EAEQ,mBAAmB;AACzB,SAAK,YAAY,YAAY,KAAK,YAAY,UAC3C,QAAQ,iBAAiB,EAAE,EAC3B,KAAK;EACV;AACF;", "names": ["lerp"]}