/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'serif': ['Playfair Display', 'serif'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'script': ['Dancing Script', 'cursive'],
      },
      colors: {
        // Luxury Gold Palette
        gold: {
          50: '#fefdf8',
          100: '#fdf9e7',
          200: '#faf0c4',
          300: '#f6e397',
          400: '#f0d068',
          500: '#e8bc44',
          600: '#d4a332',
          700: '#b08528',
          800: '#8f6a26',
          900: '#775724',
          950: '#442f11',
        },
        // Sophisticated Charcoal
        charcoal: {
          50: '#f6f6f6',
          100: '#e7e7e7',
          200: '#d1d1d1',
          300: '#b0b0b0',
          400: '#888888',
          500: '#6d6d6d',
          600: '#5d5d5d',
          700: '#4f4f4f',
          800: '#454545',
          900: '#3d3d3d',
          950: '#1a1a1a',
        },
        // Elegant Cream
        cream: {
          50: '#fefefe',
          100: '#fefcf8',
          200: '#fdf8f0',
          300: '#fbf2e6',
          400: '#f8ebd9',
          500: '#f4e2c8',
          600: '#edd5b3',
          700: '#e3c394',
          800: '#d6ad75',
          900: '#c8955c',
          950: '#6b4a2e',
        },
        // Deep Emerald (for Islamic touch)
        emerald: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          950: '#052e16',
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        '144': '36rem',
      },
      fontSize: {
        '7xl': '5rem',
        '8xl': '6rem',
        '9xl': '8rem',
      },
      letterSpacing: {
        'luxury': '0.025em',
        'wide': '0.05em',
        'wider': '0.1em',
      },
      backdropBlur: {
        'xs': '2px',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'shimmer': 'shimmer 2.5s linear infinite',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'fade-in-down': 'fadeInDown 0.8s ease-out',
        'scale-in': 'scaleIn 0.6s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 20px rgba(232, 188, 68, 0.3)' },
          '100%': { boxShadow: '0 0 30px rgba(232, 188, 68, 0.6)' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInDown: {
          '0%': { opacity: '0', transform: 'translateY(-30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },
      boxShadow: {
        'luxury': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'luxury-lg': '0 35px 60px -12px rgba(0, 0, 0, 0.3)',
        'gold': '0 10px 25px rgba(232, 188, 68, 0.3)',
        'gold-lg': '0 20px 40px rgba(232, 188, 68, 0.4)',
        'inner-luxury': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.1)',
      },
      gradients: {
        'luxury-gold': 'linear-gradient(135deg, #f6e397 0%, #e8bc44 100%)',
        'luxury-dark': 'linear-gradient(135deg, #1a1a1a 0%, #3d3d3d 100%)',
        'luxury-cream': 'linear-gradient(135deg, #fefefe 0%, #f4e2c8 100%)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
