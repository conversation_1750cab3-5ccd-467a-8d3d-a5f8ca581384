@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Dancing+Script:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --luxury-gold: #e8bc44;
  --luxury-gold-light: #f6e397;
  --luxury-charcoal: #1a1a1a;
  --luxury-cream: #fefefe;
  --luxury-emerald: #22c55e;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  color: var(--luxury-charcoal);
  background-color: var(--luxury-cream);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 26, 26, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--luxury-gold), var(--luxury-gold-light));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--luxury-gold-light), var(--luxury-gold));
}

/* Luxury Button Styles */
.btn-luxury {
  @apply relative overflow-hidden px-8 py-4 font-medium text-white bg-gradient-to-r from-gold-500 to-gold-600 rounded-full shadow-gold transition-all duration-300 hover:shadow-gold-lg hover:scale-105;
}

.btn-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-luxury:hover::before {
  left: 100%;
}

/* Glassmorphism Effect */
.glass {
  @apply backdrop-blur-md bg-white/10 border border-white/20 shadow-luxury;
}

.glass-dark {
  @apply backdrop-blur-md bg-charcoal-900/10 border border-charcoal-700/20 shadow-luxury;
}

/* Luxury Text Styles */
.text-luxury-gradient {
  background: linear-gradient(135deg, var(--luxury-gold), var(--luxury-gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow-luxury {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Magnetic Effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: translateY(-2px);
}

/* Floating Animation */
.floating {
  animation: float 6s ease-in-out infinite;
}

/* Custom Cursor */
.cursor-luxury {
  cursor: none;
}

.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--luxury-gold), var(--luxury-gold-light));
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  mix-blend-mode: difference;
}

.custom-cursor.hover {
  transform: scale(1.5);
}

/* Section Spacing */
.section-padding {
  @apply py-20 lg:py-32;
}

/* Luxury Card */
.luxury-card {
  @apply bg-white/80 backdrop-blur-sm border border-gold-200/30 rounded-2xl shadow-luxury hover:shadow-luxury-lg transition-all duration-500 hover:-translate-y-2;
}

/* Parallax Container */
.parallax-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.parallax-element {
  transform-style: preserve-3d;
}

/* Loading Animation */
.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(232, 188, 68, 0.3), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Responsive Typography */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 12px;
  }
}
