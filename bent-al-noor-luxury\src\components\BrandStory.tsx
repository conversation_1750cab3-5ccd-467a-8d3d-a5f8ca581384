import { motion, useScroll, useTransform, useInView } from 'framer-motion'
import { useRef } from 'react'
import { Quote } from 'lucide-react'

const BrandStory = () => {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "-20%"])
  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0])

  return (
    <section id="about" ref={ref} className="relative section-padding overflow-hidden">
      {/* Background with Parallax */}
      <motion.div 
        style={{ y }}
        className="absolute inset-0 bg-gradient-to-br from-charcoal-900 via-charcoal-800 to-emerald-900"
      />
      
      {/* Overlay Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-repeat" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e8bc44' fill-opacity='0.3'%3E%3Cpath d='M40 40c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm20 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Story Content */}
          <motion.div
            style={{ opacity }}
            className="text-white"
          >
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8 }}
            >
              <h2 className="font-serif text-5xl md:text-6xl font-bold mb-8">
                <span className="text-luxury-gradient">Our Story</span>
              </h2>
              <motion.div
                initial={{ width: 0 }}
                animate={isInView ? { width: "150px" } : {}}
                transition={{ duration: 1, delay: 0.3 }}
                className="h-1 bg-gradient-to-r from-gold-500 to-transparent mb-8"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-6 text-lg leading-relaxed"
            >
              <p className="text-cream-100">
                Born from a vision to bridge the gap between traditional Islamic values and contemporary luxury, 
                <span className="text-gold-300 font-medium"> Bent Al Noor</span> represents more than just fashion—it's a celebration of faith, femininity, and elegance.
              </p>
              
              <p className="text-cream-200">
                Our journey began with a simple belief: that modest fashion should never compromise on luxury or style. 
                Each scarf in our collection is a testament to this philosophy, crafted with the finest materials and 
                designed to empower the modern Muslim woman.
              </p>

              <p className="text-cream-200">
                From the bustling souks of Istanbul to the silk workshops of Lyon, we source only the most exquisite 
                materials. Our artisans, many of whom have inherited their skills through generations, pour their 
                expertise and passion into every piece.
              </p>
            </motion.div>

            {/* Quote */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-12 p-6 border-l-4 border-gold-500 bg-white/5 backdrop-blur-sm rounded-r-lg"
            >
              <Quote className="text-gold-400 mb-4" size={32} />
              <blockquote className="text-xl italic text-cream-100 mb-4">
                "Modesty is not about hiding your beauty, but about revealing your character. 
                Our scarves are designed to honor both."
              </blockquote>
              <cite className="text-gold-300 font-medium">— Founder, Bent Al Noor</cite>
            </motion.div>
          </motion.div>

          {/* Visual Elements */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            {/* Main Image */}
            <div className="relative">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.6 }}
                className="relative overflow-hidden rounded-2xl shadow-luxury-lg"
              >
                <img
                  src="https://images.unsplash.com/photo-1594734415578-00fc9540929b?fm=jpg&q=80&w=600&h=800&fit=crop&crop=center"
                  alt="Artisan crafting luxury scarf"
                  className="w-full h-[600px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-charcoal-900/50 to-transparent" />
              </motion.div>

              {/* Floating Stats */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={isInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="absolute -top-8 -left-8 glass p-6 rounded-xl text-center"
              >
                <div className="text-3xl font-bold text-luxury-gradient">15+</div>
                <div className="text-sm text-charcoal-700">Years of Excellence</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={isInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.6, delay: 1 }}
                className="absolute -bottom-8 -right-8 glass p-6 rounded-xl text-center"
              >
                <div className="text-3xl font-bold text-luxury-gradient">100%</div>
                <div className="text-sm text-charcoal-700">Handcrafted</div>
              </motion.div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-16 -right-16 w-32 h-32 border border-gold-300/30 rounded-full opacity-50" />
            <div className="absolute -bottom-16 -left-16 w-24 h-24 border border-emerald-300/30 rounded-full opacity-50" />
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-24 text-center"
        >
          <h3 className="font-serif text-4xl font-bold text-luxury-gradient mb-12">
            Our Values
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Faith",
                arabicTitle: "إيمان",
                description: "Honoring Islamic principles in every design decision"
              },
              {
                title: "Quality",
                arabicTitle: "جودة",
                description: "Uncompromising standards in materials and craftsmanship"
              },
              {
                title: "Elegance",
                arabicTitle: "أناقة",
                description: "Timeless beauty that transcends fashion trends"
              }
            ].map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 1.4 + index * 0.2 }}
                className="glass-dark p-8 rounded-xl text-center"
              >
                <h4 className="font-serif text-2xl font-semibold text-gold-300 mb-2">
                  {value.title}
                </h4>
                <p className="font-script text-gold-400 text-lg mb-4">
                  {value.arabicTitle}
                </p>
                <p className="text-cream-200 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default BrandStory
