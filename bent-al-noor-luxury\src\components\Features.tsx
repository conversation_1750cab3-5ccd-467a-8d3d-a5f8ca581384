import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import { Sparkles, Heart, Shield, Truck, Award, Users } from 'lucide-react'

const Features = () => {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const features = [
    {
      icon: Sparkles,
      title: "Premium Materials",
      arabicTitle: "مواد فاخرة",
      description: "Only the finest silk and premium fabrics, sourced from the world's most renowned textile regions.",
      gradient: "from-gold-400 to-gold-600"
    },
    {
      icon: Heart,
      title: "Handcrafted with Love",
      arabicTitle: "مصنوع بحب",
      description: "Each scarf is meticulously handcrafted by skilled artisans who pour their passion into every stitch.",
      gradient: "from-emerald-400 to-emerald-600"
    },
    {
      icon: Shield,
      title: "Modest Elegance",
      arabicTitle: "أناقة محتشمة",
      description: "Designed to honor Islamic values while embracing contemporary fashion and sophisticated style.",
      gradient: "from-charcoal-400 to-charcoal-600"
    },
    {
      icon: Award,
      title: "Award-Winning Design",
      arabicTitle: "تصميم حائز على جوائز",
      description: "Recognized internationally for excellence in Islamic fashion and innovative design approaches.",
      gradient: "from-gold-500 to-amber-600"
    },
    {
      icon: Truck,
      title: "Worldwide Delivery",
      arabicTitle: "توصيل عالمي",
      description: "Luxury packaging and secure worldwide shipping to bring our collection to your doorstep.",
      gradient: "from-blue-400 to-blue-600"
    },
    {
      icon: Users,
      title: "Community of Sisters",
      arabicTitle: "مجتمع الأخوات",
      description: "Join thousands of women worldwide who choose Bent Al Noor for their modest luxury needs.",
      gradient: "from-purple-400 to-purple-600"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section ref={ref} className="section-padding bg-gradient-to-b from-white via-cream-50 to-gold-50/30">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <h2 className="font-serif text-5xl md:text-6xl font-bold text-luxury-gradient mb-6">
            Why Choose Bent Al Noor
          </h2>
          <motion.div
            initial={{ width: 0 }}
            animate={isInView ? { width: "250px" } : {}}
            transition={{ duration: 1, delay: 0.3 }}
            className="h-1 bg-gradient-to-r from-transparent via-gold-500 to-transparent mx-auto mb-8"
          />
          <p className="text-xl text-charcoal-600 max-w-4xl mx-auto leading-relaxed">
            We believe that modesty and luxury are not mutually exclusive. Our commitment to excellence 
            ensures that every piece reflects the highest standards of quality, design, and Islamic values.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group relative luxury-card p-8 text-center cursor-pointer"
            >
              {/* Background Gradient on Hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-2xl`} />
              
              {/* Icon */}
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br ${feature.gradient} text-white mb-6 shadow-lg`}
              >
                <feature.icon size={28} />
              </motion.div>

              {/* Content */}
              <div className="relative z-10">
                <h3 className="font-serif text-2xl font-semibold text-charcoal-800 mb-2">
                  {feature.title}
                </h3>
                <p className="font-script text-gold-600 text-lg mb-4">
                  {feature.arabicTitle}
                </p>
                <p className="text-charcoal-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>

              {/* Decorative Elements */}
              <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border border-gold-300 rounded-full"
                />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { number: "10K+", label: "Happy Customers", arabicLabel: "عميلة سعيدة" },
            { number: "50+", label: "Countries Served", arabicLabel: "دولة نخدمها" },
            { number: "99%", label: "Satisfaction Rate", arabicLabel: "معدل الرضا" },
            { number: "5★", label: "Average Rating", arabicLabel: "متوسط التقييم" }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-4xl md:text-5xl font-bold text-luxury-gradient mb-2">
                {stat.number}
              </div>
              <div className="text-charcoal-700 font-medium">
                {stat.label}
              </div>
              <div className="text-gold-600 font-script text-sm">
                {stat.arabicLabel}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default Features
